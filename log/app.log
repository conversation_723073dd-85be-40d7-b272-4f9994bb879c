2025-10-09 09:47:26.922 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | main | ERROR | org.apache.dubbo.common.Version |  | Version.java:252 |  [DUBBO] Duplicate class org/apache/dubbo/common/Version.class in 2 jar [file:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo-common/2.7.14/dubbo-common-2.7.14.jar!/org/apache/dubbo/common/Version.class, file:/Users/<USER>/.m2/repository/org/apache/dubbo/dubbo/2.7.14/dubbo-2.7.14.jar!/org/apache/dubbo/common/Version.class], dubbo version: 2.7.14, current host: **********
2025-10-09 09:47:26.963 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | background-preinit | INFO  | org.hibernate.validator.internal.util.Version |  | Version.java:30 | HV000001: Hibernate Validator 5.3.4.Final
2025-10-09 09:47:26.992 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | main | INFO  | com.extracme.evcard.mtc.EvcardMtcApplication |  | StartupInfoLogger.java:55 | Starting EvcardMtcApplication on maoyijiadeMacBook-Air.local with PID 91617 (/Users/<USER>/projects/evcard-mtc/evcard-mtc-rest/target/classes started by maoyijia in /Users/<USER>/projects/evcard-mtc)
2025-10-09 09:47:26.992 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | main | DEBUG | com.extracme.evcard.mtc.EvcardMtcApplication |  | StartupInfoLogger.java:56 | Running with Spring Boot v2.3.12.RELEASE, Spring v5.2.15.RELEASE
2025-10-09 09:47:26.992 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | main | INFO  | com.extracme.evcard.mtc.EvcardMtcApplication |  | SpringApplication.java:652 | The following profiles are active: siac
2025-10-09 09:47:27.724 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | main | INFO  | com.alibaba.spring.util.BeanRegistrar |  | BeanRegistrar.java:67 | The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
2025-10-09 09:47:27.724 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | main | INFO  | com.alibaba.spring.util.BeanRegistrar |  | BeanRegistrar.java:67 | The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
2025-10-09 09:47:27.725 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | main | INFO  | com.alibaba.spring.util.BeanRegistrar |  | BeanRegistrar.java:67 | The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboApplicationListenerRegistrar]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboApplicationListenerRegister] has been registered.
2025-10-09 09:47:27.725 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | main | INFO  | com.alibaba.spring.util.BeanRegistrar |  | BeanRegistrar.java:67 | The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
2025-10-09 09:47:27.725 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | main | INFO  | com.alibaba.spring.util.BeanRegistrar |  | BeanRegistrar.java:67 | The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigEarlyInitializationPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigEarlyInitializationPostProcessor] has been registered.
2025-10-09 09:47:27.725 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | main | WARN  | c.e.evcard.redis.spring.JedisUtilPostProcessor |  | JedisUtilPostProcessor.java:54 | 使用了废弃的redis配置属性，pwd不可作为属性key。@see System.getenv("PWD")
2025-10-09 09:47:27.728 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | main | INFO  | com.extracme.evcard.redis.JedisUtil |  | JedisUtil.java:105 | 链接redis evcard-st-lan.redis.rds.aliyuncs.com : 6379 pwd:Wp4uJK*Vc3v2
2025-10-09 09:47:27.735 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | main | INFO  | com.extracme.evcard.redis.JedisUtil |  | JedisUtil.java:112 | 初始化redis成功evcard-st-lan.redis.rds.aliyuncs.com : 6379 pwd:Wp4uJK*Vc3v2
2025-10-09 09:47:28.115 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | main | INFO  | c.a.s.b.f.a.ConfigurationBeanBindingRegistrar |  | ConfigurationBeanBindingRegistrar.java:139 | The configuration bean definition [name : org.apache.dubbo.config.ApplicationConfig#0, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2025-10-09 09:47:28.115 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | main | INFO  | com.alibaba.spring.util.BeanRegistrar |  | BeanRegistrar.java:67 | The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
2025-10-09 09:47:28.115 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | main | INFO  | c.a.s.b.f.a.ConfigurationBeanBindingRegistrar |  | ConfigurationBeanBindingRegistrar.java:139 | The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2025-10-09 09:47:28.115 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | main | INFO  | c.a.s.b.f.a.ConfigurationBeanBindingRegistrar |  | ConfigurationBeanBindingRegistrar.java:139 | The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2025-10-09 09:47:28.116 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | main | INFO  | c.a.s.b.f.a.ConfigurationBeanBindingRegistrar |  | ConfigurationBeanBindingRegistrar.java:139 | The configuration bean definition [name : org.apache.dubbo.config.ConsumerConfig#0, content : Root bean: class [org.apache.dubbo.config.ConsumerConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2025-10-09 09:47:28.174 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | main | INFO  | com.alibaba.spring.util.BeanRegistrar |  | BeanRegistrar.java:67 | The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
2025-10-09 09:47:28.189 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | main | DEBUG | org.apache.ibatis.logging.LogFactory |  | JakartaCommonsLoggingImpl.java:54 | Logging initialized using 'class org.apache.ibatis.logging.commons.JakartaCommonsLoggingImpl' adapter.
2025-10-09 09:47:28.193 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | main | INFO  | o.s.c.annotation.ConfigurationClassPostProcessor |  | ConfigurationClassPostProcessor.java:413 | Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2025-10-09 09:47:28.478 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | main | INFO  | o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker |  | PostProcessorRegistrationDelegate.java:335 | Bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$DataSourceTransactionManagerConfiguration' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$DataSourceTransactionManagerConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-10-09 09:47:28.482 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | main | INFO  | o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker |  | PostProcessorRegistrationDelegate.java:335 | Bean 'com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure' of type [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure$$EnhancerBySpringCGLIB$$5fa37490] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-10-09 09:47:28.484 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | main | INFO  | c.a.d.s.b.a.DruidDataSourceAutoConfigure |  | DruidDataSourceAutoConfigure.java:56 | Init DruidDataSource
2025-10-09 09:47:28.526 | spring.profiles.active_IS_UNDEFINED | evcard-mtc | main | INFO  | o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker |  | PostProcessorRegistrationDelegate.java:335 | Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
